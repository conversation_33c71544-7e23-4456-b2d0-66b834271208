<script setup>
import { cn } from "@/lib/utils";
import { Primitive } from "reka-ui";
import { buttonVariants } from ".";
import { ref } from "vue";
import { HomeIcon, SpinnerIcon } from "../../icons";
import { safeCallRefMethod } from "@/utils/domHelpers";

const props = defineProps({
  variant: { type: null, required: false },
  size: { type: null, required: false },
  class: { type: null, required: false },
  asChild: { type: Boolean, required: false },
  as: { type: null, required: false, default: "button" },
  loading: { type: Boolean, default: false },
});

const homeIconRef = ref(null);
const transparentHomeIconRef = ref(null);

function handleHomeButtonMouseEnter() {
  const iconRef =
    props.variant === "transparentHome" ? transparentHomeIconRef : homeIconRef;
  safeCallRefMethod(iconRef, "startAnimation");
}

function handleHomeButtonMouseLeave() {
  const iconRef =
    props.variant === "transparentHome" ? transparentHomeIconRef : homeIconRef;
  safeCallRefMethod(iconRef, "stopAnimation");
}
</script>

<template>
  <Primitive
    :as="props.as"
    :as-child="props.asChild"
    :class="[
      props.variant === 'transparentHome'
        ? 'transparent-home-button'
        : cn(
            buttonVariants({ variant: props.variant, size: props.size }),
            props.class,
            [
              'skillGenerate',
              'skillAdd',
              'assessmentGenerate',
              'sessionGenerate',
              'reportGenerate',
              'generalAction',
            ].includes(props.variant)
              ? 'skill-gradient-button'
              : props.variant === 'submitQuiz'
                ? 'submit-quiz-button'
                : [
                      'skillBack',
                      'assessmentBack',
                      'sessionBack',
                      'userBack',
                      'back',
                    ].includes(props.variant)
                  ? 'skill-back-button'
                  : ['quickAction', 'homeCardButton', 'homeCardNav'].includes(
                        props.variant,
                      )
                    ? ''
                    : '',
            { 'opacity-75 cursor-not-allowed': props.loading },
            { 'home-button-hover': props.variant === 'homeButton' },
          ),
    ]"
    :disabled="props.loading"
    @mouseenter="
      props.variant === 'homeButton' || props.variant === 'transparentHome'
        ? handleHomeButtonMouseEnter()
        : null
    "
    @mouseleave="
      props.variant === 'homeButton' || props.variant === 'transparentHome'
        ? handleHomeButtonMouseLeave()
        : null
    "
  >
    <template #default>
      <span v-if="props.loading" class="flex justify-center items-center">
        <SpinnerIcon :size="20" />
      </span>
      <span
        v-else-if="props.variant === 'homeButton'"
        class="flex items-center justify-center"
      >
        <HomeIcon
          ref="homeIconRef"
          :size="25"
          class="home-icon !w-[30px] !h-[30px]"
        />
      </span>
      <span
        v-else-if="props.variant === 'transparentHome'"
        class="flex items-center justify-center bg-transparent"
      >
        <HomeIcon
          ref="transparentHomeIconRef"
          :size="30"
          class="transparent-home-icon !w-[35px] !h-[35px]"
        />
      </span>
      <span v-else class="button-text flex items-center justify-center">
        <slot />
      </span>
    </template>
  </Primitive>
</template>

<style scoped>
/* Global rule to ensure button text remains visible on hover */
.skill-gradient-button:hover span,
.skill-back-button:hover span,
.submit-quiz-button:hover span,
.btn-phantom:hover span,
.btn-phantom-secondary:hover span,
button:hover span,
a.btn-phantom:hover span,
a.btn-phantom-secondary:hover span {
  opacity: 1 !important;
  visibility: visible !important;
  color: inherit !important;
}

.home-button-hover:hover {
  background-color: rgb(31, 41, 55);
  color: white; /* Changed from rgb(31, 41, 55) to white to keep text visible */
  transform: scale(1.05);
}

.home-icon {
  color: rgb(31, 41, 55);
  transition: color 0.3s ease;
  width: 30px !important;
  height: 30px !important;
}

.home-button-hover:hover .home-icon {
  color: rgb(103, 232, 249);
}

/* Transparent Home Button Styles */
.transparent-home-button {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 50;
  background-color: transparent !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  padding: 8px;
  margin: 0;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.transparent-home-button:hover {
  background-color: transparent !important;
  transform: scale(1.05);
}

.transparent-home-icon {
  color: rgb(103, 232, 249);
  transition: all 0.3s ease;
  width: 35px !important;
  height: 35px !important;
  filter: drop-shadow(0 0 4px rgba(103, 232, 249, 0.7));
  stroke-width: 2.5;
}

.transparent-home-button:hover .transparent-home-icon {
  color: rgb(103, 232, 249);
  filter: drop-shadow(0 0 10px rgba(103, 232, 249, 1));
  transform: scale(1.15);
  stroke-width: 2.5;
}

/* Skill Button Styles */
.skill-gradient-button {
  position: relative;
  overflow: hidden;
  background: linear-gradient(to right, rgb(6, 182, 212), rgb(59, 130, 246));
  color: white;
  font-weight: 500;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  transform: translateZ(0);
  border-radius: 0.5rem;
}

.skill-gradient-button:hover {
  transform: translateY(-1px);
  box-shadow:
    0 10px 15px -3px rgba(6, 182, 212, 0.3),
    0 4px 6px -2px rgba(6, 182, 212, 0.2);
  color: white; /* Added to ensure text remains visible on hover */
}

.skill-gradient-button::before {
  content: "";
  position: absolute;
  inset: 0;
  background: linear-gradient(to right, rgb(8, 145, 178), rgb(37, 99, 235));
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 0;
}

.skill-gradient-button:hover::before {
  opacity: 1;
}

.skill-gradient-button::after {
  content: "";
  position: absolute;
  inset: -1px;
  background: linear-gradient(
    to right,
    rgba(6, 182, 212, 0.3),
    rgba(59, 130, 246, 0.3)
  );
  opacity: 0.3;
  filter: blur(4px);
  transition: all 0.3s ease;
  z-index: -1;
}

.skill-gradient-button:hover::after {
  opacity: 0.5;
  filter: blur(6px);
}

.skill-gradient-button > * {
  position: relative;
  z-index: 1;
}

.skill-back-button {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  font-weight: 500;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
  opacity: 1;
}

.skill-back-button:hover {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white !important;
}

.skill-back-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Submit Quiz Button Styles */
.submit-quiz-button {
  position: relative;
  overflow: hidden;
  background: linear-gradient(to right, rgb(239, 68, 68), rgb(220, 38, 38));
  color: white;
  font-weight: 500;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  transform: translateZ(0);
  border-radius: 0.5rem;
}

.submit-quiz-button:hover {
  transform: translateY(-1px);
  box-shadow:
    0 10px 15px -3px rgba(239, 68, 68, 0.3),
    0 4px 6px -2px rgba(239, 68, 68, 0.2);
  color: white !important; /* Added !important to ensure text remains visible on hover */
}

.submit-quiz-button::before {
  content: "";
  position: absolute;
  inset: 0;
  background: linear-gradient(to right, rgb(220, 38, 38), rgb(185, 28, 28));
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 0;
}

.submit-quiz-button:hover::before {
  opacity: 1;
}

.submit-quiz-button::after {
  content: "";
  position: absolute;
  inset: -1px;
  background: linear-gradient(
    to right,
    rgba(239, 68, 68, 0.3),
    rgba(220, 38, 38, 0.3)
  );
  opacity: 0.3;
  filter: blur(4px);
  transition: all 0.3s ease;
  z-index: -1;
}

.submit-quiz-button:hover::after {
  opacity: 0.5;
  filter: blur(6px);
}

.submit-quiz-button > *,
.button-text {
  position: relative;
  z-index: 1;
}

/* Ensure button text is always visible */
.button-text,
.button-text *,
span,
.Primitive span,
.Primitive * {
  opacity: 1 !important;
  visibility: visible !important;
  z-index: 10;
  position: relative;
  color: inherit !important;
}

/* Override any hover effects that might hide text */
*:hover .button-text,
*:hover span,
.Primitive:hover span,
.Primitive:hover * {
  opacity: 1 !important;
  visibility: visible !important;
  color: inherit !important;
}
</style>
