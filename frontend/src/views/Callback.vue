<template>
  <div class="min-h-screen flex items-center justify-center">
    <div
      class="loading-message text-center p-8 bg-gray-900/70 backdrop-blur-md rounded-xl border border-cyan-500/30 shadow-glow-md"
    >
      <div class="flex flex-col items-center">
        <!-- Loading spinner -->
        <svg
          class="animate-spin h-10 w-10 text-cyan-500 mb-4"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            class="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="4"
          />
          <path
            class="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
        <h2
          class="text-xl font-bold bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent mb-2"
        >
          HERBIT
        </h2>
        <p class="text-gray-300">Processing authentication...</p>
        <p class="text-gray-400 text-sm mt-2">You will be redirected shortly</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted } from "vue";
import { useRouter } from "vue-router";
import { info, warning, error, logUserAction } from "@/utils/logger";
import { api } from "@/services/api";

const router = useRouter();

onMounted(async () => {
  const params = new URLSearchParams(window.location.search);
  const code = params.get("code");
  const receivedState = params.get("state");
  const storedState = localStorage.getItem("oauth_state");



  // Clear the stored state to prevent reuse
  localStorage.removeItem("oauth_state");

  if (!code) {
    warning("No code in URL, redirecting to login");
    router.push("/login");
    return;
  }

  if (code.length < 20) {
    warning(
      "Authorization code appears to be too short, this might indicate a problem",
    );
  }

  // Check if this code has already been used
  const usedCode = localStorage.getItem("used_auth_code");
  if (usedCode === code) {
    warning("Authorization code has already been used, redirecting to login");
    router.push("/login");
    return;
  }

  // Mark this code as used
  localStorage.setItem("used_auth_code", code);

  // Verify state parameter to prevent CSRF attacks
  if (!receivedState || receivedState !== storedState) {
    warning("State parameter mismatch or missing", {
      receivedState,
      storedState,
    });
    // We'll continue anyway since we're using a direct token exchange on the backend
    // that doesn't rely on the state parameter for security
  }

  const data = {
    code: code,
    redirect_uri: import.meta.env.VITE_AUTH_CALLBACK_URL,
    state: receivedState, // Include the state in the token request
  };

  try {
    // Use the API base URL from environment
    const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || "";
    const tokenUrl = apiBaseUrl
      ? `${apiBaseUrl}/auth/token`
      : "/api/auth/token";



    const response = await fetch(tokenUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
      credentials: "include", // Include cookies for session handling
    });

    if (response.ok) {
      await response.json();


      // Authentication successful - clear cache and fetch fresh user info
      try {
        // Clear the cached userinfo to force a fresh fetch
        api.clearUserInfoCache();

        // Fetch fresh user info from session
        const userInfoData = await api.getCachedUserInfo(true); // Force refresh
        if (userInfoData.authenticated && userInfoData.user) {
          localStorage.setItem("user_info", JSON.stringify(userInfoData.user));

        }
      } catch (userInfoError) {
        error("Error fetching user info:", { error: userInfoError });
      }

      // Add a small delay to ensure the user info is properly cached before navigation
      await new Promise((resolve) => setTimeout(resolve, 100));



      // Trigger a localStorage event to notify other components about the authentication change
      // This is needed because the storage event only fires when localStorage is changed from another tab
      window.dispatchEvent(new Event("auth-state-changed"));

      // Clear the used code after successful authentication
      localStorage.removeItem("used_auth_code");

      // Redirect to root path - the DynamicHome component will handle showing the appropriate view
      info("Authentication successful, redirecting to home");
      logUserAction("authentication_success");
      router.push("/");
    } else {
      // Try to parse JSON response, but handle cases where it's not JSON
      let errorResult = null;
      try {
        const responseText = await response.text();
        if (responseText) {
          // Try to parse as JSON first
          try {
            errorResult = JSON.parse(responseText);
          } catch {
            // If JSON parsing fails, use the raw text
            errorResult = { message: responseText };
          }
        } else {
          errorResult = { message: "Empty response from server" };
        }
      } catch {
        errorResult = { message: "Failed to read response from server" };
      }

      error("Authentication failed:", {
        status: response.status,
        statusText: response.statusText,
        errorResult,
      });
      logUserAction("authentication_failed", {
        status: response.status,
        errorResult,
      });

      // Store error details for the error page
      localStorage.setItem(
        "auth_error",
        JSON.stringify({
          status: response.status,
          statusText: response.statusText,
          error: errorResult,
          timestamp: new Date().toISOString(),
          context: "token_exchange",
        }),
      );

      router.push("/error?error=authentication_failed"); // Redirect to error page
    }
  } catch (err) {
    error("Network or server error during authentication:", { error: err });
    logUserAction("authentication_error", { error: err.message });

    // Store error details for the error page
    localStorage.setItem(
      "auth_error",
      JSON.stringify({
        error: err.message,
        stack: err.stack,
        timestamp: new Date().toISOString(),
        context: "network_error",
      }),
    );

    router.push("/error?error=server_error"); // Redirect to error page
  }
});
</script>
