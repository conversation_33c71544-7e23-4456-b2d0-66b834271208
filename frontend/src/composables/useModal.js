/**
 * Modal/Dialog Management Composable
 *
 * Provides specialized modal composable for detailed results popups
 */
import { ref, onUnmounted } from "vue";

/**
 * Specialized modal composable for detailed results popups
 * Used in quiz components for showing detailed results
 */
export function useDetailedResultsModal() {
  const isOpen = ref(false);
  const isAnimating = ref(false);

  // Store original body overflow for restoration
  let originalBodyOverflow = "";
  let originalDocumentOverflow = "";

  /**
   * Open the modal
   */
  const open = () => {
    if (isOpen.value) return;

    isOpen.value = true;
    isAnimating.value = true;

    // Prevent body scrolling
    originalBodyOverflow = document.body.style.overflow;
    originalDocumentOverflow = document.documentElement.style.overflow;

    document.body.style.overflow = "hidden";
    document.documentElement.style.overflow = "hidden";
    document.body.classList.add("modal-open");

    // Add escape key listener
    document.addEventListener("keydown", handleEscapeKey);

    // Animation timeout
    setTimeout(() => {
      isAnimating.value = false;
    }, 300);
  };

  /**
   * Close the modal
   */
  const close = () => {
    if (!isOpen.value) return;

    isAnimating.value = true;

    // Remove escape key listener
    document.removeEventListener("keydown", handleEscapeKey);

    setTimeout(() => {
      isOpen.value = false;
      isAnimating.value = false;

      // Restore body scrolling
      document.body.style.overflow = originalBodyOverflow;
      document.documentElement.style.overflow = originalDocumentOverflow;
      document.body.classList.remove("modal-open");
    }, 200);
  };

  /**
   * Toggle the modal state
   */
  const toggle = () => {
    if (isOpen.value) {
      close();
    } else {
      open();
    }
  };

  /**
   * Handle escape key press
   */
  const handleEscapeKey = (event) => {
    if (event.key === "Escape" && isOpen.value) {
      close();
    }
  };

  /**
   * Handle backdrop click
   */
  const handleBackdropClick = (event) => {
    if (event.target === event.currentTarget) {
      close();
    }
  };

  /**
   * Cleanup function
   */
  const cleanup = () => {
    document.removeEventListener("keydown", handleEscapeKey);

    // Restore scrolling if modal was open
    if (isOpen.value) {
      document.body.style.overflow = originalBodyOverflow;
      document.documentElement.style.overflow = originalDocumentOverflow;
      document.body.classList.remove("modal-open");
    }
  };

  // Cleanup on unmount
  onUnmounted(cleanup);

  return {
    isOpen,
    isAnimating,
    open,
    close,
    toggle,
    handleBackdropClick,
    cleanup,
  };
}
