import { onMounted, onUnmounted } from "vue";

/**
 * Composable to manage page scrolling
 * Disables scrolling on mount and re-enables on unmount
 */
export function useScrollManagement() {
  onMounted(() => {
    document.body.style.overflow = "hidden";
    document.documentElement.style.overflow = "hidden";
  });

  onUnmounted(() => {
    document.body.style.overflow = "";
    document.documentElement.style.overflow = "";
  });
}
