#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to manually send quiz invitation email for a specific session.
"""

import asyncio
import sys
import requests
from pathlib import Path

# Add app to Python path
sys.path.insert(0, '/home/<USER>/git-prep/herbit/backend')

async def send_quiz_invitation_for_session():
    """Send quiz invitation email manually for a session."""
    
    from dotenv import load_dotenv
    
    # Load environment variables
    backend_dir = Path('/home/<USER>/git-prep/herbit/backend')
    env_path = backend_dir / '.env'
    load_dotenv(dotenv_path=env_path)
    
    # Import email service
    from app.services.email_service import EmailService
    
    # Create fresh email service instance
    email_service = EmailService()
    
    # Email configuration
    to_email = "<EMAIL>"
    username = "Test User"
    assessment_name = "Sample Assessment"
    session_code = "123456"
    frontend_url = "http://localhost:5173"
    quiz_link = f"{frontend_url}/quiz/encoded_session_code"
    
    print(f"Sending quiz invitation email...")
    print(f"  To: {to_email}")
    print(f"  Username: {username}")
    print(f"  Assessment: {assessment_name}")
    print(f"  Session Code: {session_code}")
    print(f"  Quiz Link: {quiz_link}")
    
    # Send email
    result = await email_service.send_quiz_invitation(
        to_email=to_email,
        username=username,
        assessment_name=assessment_name,
        session_code=session_code,
        quiz_link=quiz_link
    )
    
    print(f"Email sent successfully: {result}")
    
    if result:
        print(f"✅ Email sent to {to_email}")
        print(f"The user should receive a quiz invitation email with:")
        print(f"  - Assessment: {assessment_name}")
        print(f"  - Session Code: {session_code}")
        print(f"  - Quiz Link: {quiz_link}")
    else:
        print(f"❌ Failed to send email to {to_email}")

if __name__ == "__main__":
    asyncio.run(send_quiz_invitation_for_session())
