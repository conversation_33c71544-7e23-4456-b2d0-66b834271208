"""
Sessions-related API routes for the quiz/assessment management system.
"""

import os
from typing import Dict, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, Request, status
from pydantic import BaseModel

from ...api.middlewares.hashid_middleware import hash_ids_in_response
from ...models.assessment_manager import (
    calculate_total_score_for_assessment,
    get_assessment_by_id,
    get_performance_level_with_correct_total,
    get_session_and_assessment_details_by_code,
)
from ...models.sessions_manager import (
    calculate_session_scores,
    complete_session_in_db,
    create_session_in_db,
    find_or_create_user_by_details,
    generate_unique_session_code,
    get_completed_session_for_results,
    get_or_create_user,
    get_session_answers_for_results,
    get_session_for_start_or_validation,
    get_session_user_details,
    get_sessions_by_user_id,
    get_sessions_count,
    get_sessions_data,
    get_single_session_data,
    get_user_id_by_email,
    handle_completed_session,
    handle_expired_session,
    start_session_in_db,
    validate_assessment_exists,
    validate_session_code_format,
)
from ...utils.api_response import (
    error_response,
    paginated_response,
    raise_http_exception,
    success_response,
)
from ...utils.db_utils import safe_json_loads
from ...utils.hashid_utils import (
    decode_assessment_id,
    decode_session_code,
    detect_hash_type,
    encode_session_code,
)
from ...utils.logger import (
    debug,
    error,
    info,
    warning,
)

# Import email service after logger functions are available
try:
    from ...services.email_service import email_service
    EMAIL_SERVICE_AVAILABLE = True
    info(f"Email service imported successfully: {email_service}")
    info(f"Email service SMTP username: {email_service.smtp_username}")
except ImportError as e:
    EMAIL_SERVICE_AVAILABLE = False
    email_service = None
    error(f"Failed to import email service: {str(e)}")
except Exception as e:
    EMAIL_SERVICE_AVAILABLE = False
    email_service = None
    error(f"Error initializing email service: {str(e)}")
from ...utils.rate_limiter import rate_limiter

sessions_router = APIRouter()


def get_current_user(request: Request) -> dict:
    """Get current authenticated user from session."""
    user = request.session.get("user")
    if not user:
        raise HTTPException(status_code=401, detail="Authentication required. Please log in to access your sessions.")
    return user


class StartSessionRequest(BaseModel):
    session_code: str


class SessionCodeRequest(BaseModel):
    session_code: str


class SessionSubmissionRequest(BaseModel):
    session_code: str
    user_id: str


# =============================================================================
# GET SESSION API ENDPOINT AND HELPER FUNCTIONS
# =============================================================================


@sessions_router.get("/admin/sessions")
async def get_sessions(
    limit: int = Query(3, ge=1, le=100),
    offset: int = Query(0, ge=0),
    status_filter: Optional[str] = Query(
        None, description="Filter by session status: 'pending', 'completed', or 'all'"
    ),
):
    """
    Get sessions with pagination and optional status filtering

    Args:
        limit: Maximum number of items per page (default: 3)
        offset: Starting position (default: 0)
        status_filter: Filter by session status ('pending', 'completed', or 'all')
    """
    try:
        # Get total count
        total = get_sessions_count(status_filter)

        # Get paginated sessions data
        sessions = get_sessions_data(status_filter, limit, offset)

        # Transform response to include hashed IDs
        hashed_sessions = hash_ids_in_response(sessions)

        # Return paginated response
        return paginated_response(
            data=hashed_sessions,
            total=total,
            limit=limit,
            offset=offset,
            message="Sessions retrieved successfully",
            additional_data={"status_filter": status_filter or "all"},
        )
    except Exception as e:
        error(f"Error getting sessions: {str(e)}")
        return error_response(
            message=f"Error getting sessions: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


# =============================================================================
# GET SESSION DETAILS API ENDPOINT AND HELPER FUNCTIONS
# =============================================================================


def _enhance_completed_session_data(session_dict: dict) -> dict:
    """Add score and performance data for completed sessions."""
    if session_dict.get("status") == "completed" and session_dict.get("score") is not None:
        obtained_score = float(session_dict["score"])
        assessment_id = session_dict["assessment_id"]
        session_internal_id = session_dict["id"]

        # Calculate correct total possible score
        total_possible_score = calculate_total_score_for_assessment(assessment_id, session_internal_id)

        # Calculate performance level
        performance_level = get_performance_level_with_correct_total(obtained_score, assessment_id, session_internal_id)

        # Calculate percentage
        percentage = (obtained_score / total_possible_score * 100) if total_possible_score > 0 else 0

        # Add to session dict
        session_dict["obtained_score"] = obtained_score
        session_dict["total_possible_score"] = total_possible_score
        session_dict["percentage"] = round(percentage, 2)
        session_dict["performance_level"] = performance_level

    return session_dict


def _get_session_by_code(session_id: str) -> Optional[Dict]:
    """Get session by 6-digit session code by calling the data fetcher."""
    info(f"Using 6-digit session code: {session_id}")
    return get_single_session_data(session_code=session_id)


def _get_session_by_numeric_id(session_id: str) -> Optional[Dict]:
    """Get session by numeric ID by calling the data fetcher."""
    actual_id = int(session_id)
    info(f"Using numeric session ID: {actual_id}")
    return get_single_session_data(session_id=actual_id)


def _decode_and_get_session(session_id: str) -> Optional[Dict]:
    """Decode hash and get session using the central data fetching function."""
    info(f"Attempting to decode hash: {session_id}")
    hash_type = detect_hash_type(session_id)
    info(f"Detected hash type: {hash_type}")

    decoded_id = decode_session_code(session_id)
    if decoded_id:
        info(f"Successfully decoded as session hash: {session_id} -> {decoded_id}")
    elif hash_type == "assessment":
        decoded_id = decode_assessment_id(session_id)
        if decoded_id:
            info(f"Successfully decoded as assessment hash (treating as session): {session_id} -> {decoded_id}")

    info(f"Final decode result: {decoded_id}")

    if not decoded_id:
        warning(f"Invalid or undecodable session hash: {session_id}")
        raise HTTPException(status_code=400, detail=f"Invalid session hash: {session_id}")

    info(f"Successfully decoded {session_id} to ID: {decoded_id}")
    session_dict = get_single_session_data(session_id=decoded_id)

    if session_dict:
        # This post-processing is specific to the API response, so it stays here.
        for key, value in session_dict.items():
            if hasattr(value, "isoformat"):
                session_dict[key] = value.isoformat()
        return session_dict

    return None


def _generate_session_error_message(session_id: str) -> str:
    """Generate appropriate error message based on session_id format."""
    if len(session_id) == 6 and session_id.isdigit():
        return f"Session with 6-digit code {session_id} not found"
    elif session_id.isdigit():
        return f"Session with numeric ID {session_id} not found"
    else:
        return f"Session with ID {session_id} not found. Expected: numeric ID or 6-digit session code"


def _prepare_session_response(session_dict: dict) -> dict:
    """Prepare session dictionary for response."""
    if session_dict and "id_hash" not in session_dict:
        # Enhance completed session data
        session_dict = _enhance_completed_session_data(session_dict)

        # Convert datetime objects to ISO strings before hash transformation
        for key, value in session_dict.items():
            if hasattr(value, "isoformat"):
                session_dict[key] = value.isoformat()

        session_dict = hash_ids_in_response(session_dict)

    return session_dict


@sessions_router.get("/admin/sessions/{session_id}/details")
async def get_session_details_endpoint(session_id: str):
    """Get detailed session information by session ID (numeric ID or 6-digit session code)"""
    try:
        info(f"Session detail request for ID: {session_id} (type: {type(session_id)}, length: {len(session_id)})")

        session_dict = None

        # Check if it's a 6-digit session code first
        if len(session_id) == 6 and session_id.isdigit():
            session_dict = _get_session_by_code(session_id)
        # If it's a numeric ID (but not 6 digits), get directly by ID
        elif session_id.isdigit():
            session_dict = _get_session_by_numeric_id(session_id)
        else:
            # Try to decode hash
            session_dict = _decode_and_get_session(session_id)

        if not session_dict:
            error_detail = _generate_session_error_message(session_id)
            warning(f"Session lookup failed: {error_detail}")
            raise HTTPException(status_code=404, detail=error_detail)

        # Prepare session response
        session_dict = _prepare_session_response(session_dict)

        info(f"Returning session data keys: {list(session_dict.keys()) if session_dict else None}")

        return success_response(data=session_dict, message="Session details retrieved successfully")

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error getting session details for ID {session_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting session details: {str(e)}")


# =============================================================================
# CREATE SESSION API ENDPOINT AND HELPER FUNCTIONS
# =============================================================================


def _process_user_identifier(identifier: str, default_email_domain: str) -> tuple[str, str, str]:
    """Process user identifier and return display_name, email, and external_id."""
    if "@" in identifier:
        email = identifier.lower()
        display_name = email.split("@")[0]
    else:
        display_name = identifier
        email = f"{display_name.lower()}@{default_email_domain}"

    external_id = display_name
    return display_name, email, external_id


def _validate_session_request(request_data: dict) -> tuple[int, list[str]]:
    """Validate session generation request and return assessment_id and user_identifiers."""
    if not request_data:
        raise_http_exception(status_code=400, detail="Request data is required.")
    
    assessment_id_str = request_data.get("assessment_id")
    usernames_str = request_data.get("usernames", "")

    debug(f"Validating session request - assessment_id_str: {assessment_id_str}, usernames_str: {usernames_str}")

    if not assessment_id_str or not usernames_str:
        raise_http_exception(status_code=400, detail="Assessment ID and usernames are required.")

    try:
        assessment_id = int(assessment_id_str)
    except (ValueError, TypeError) as e:
        error(f"Invalid assessment_id format: {assessment_id_str}, error: {str(e)}")
        raise_http_exception(status_code=400, detail="Assessment ID must be a valid number.")

    user_identifiers = [identifier.strip() for identifier in usernames_str.split(",") if identifier.strip()]

    if not user_identifiers:
        raise_http_exception(status_code=400, detail="No valid usernames provided.")

    debug(f"Validation successful - assessment_id: {assessment_id}, user_identifiers: {user_identifiers}")
    return assessment_id, user_identifiers


def _process_single_user_session(identifier: str, assessment_id: int, default_email_domain: str) -> dict:
    """Process a single user session creation."""
    try:
        # Validate inputs
        if not identifier or not isinstance(identifier, str):
            raise Exception(f"Invalid identifier: {identifier}")
        if not assessment_id or not isinstance(assessment_id, int):
            raise Exception(f"Invalid assessment_id: {assessment_id}")
        if not default_email_domain:
            raise Exception(f"Invalid default_email_domain: {default_email_domain}")
            
        # Process the identifier
        display_name, email, external_id = _process_user_identifier(identifier, default_email_domain)
        
        if not display_name or not email or not external_id:
            raise Exception(f"Failed to process user identifier '{identifier}': display_name={display_name}, email={email}, external_id={external_id}")

        # Find or create the user in the database
        user_internal_id = find_or_create_user_by_details(
            display_name=display_name,
            email=email,
            external_id=external_id,
        )
        
        if not user_internal_id:
            raise Exception(f"Failed to create or find user for '{identifier}': user_internal_id is None")

        # Generate a unique session code
        session_code = generate_unique_session_code()
        
        if not session_code:
            raise Exception(f"Failed to generate session code for '{identifier}'")

        # Insert the new session into the database
        session_db_id = create_session_in_db(session_code, user_internal_id, assessment_id)
        
        if not session_db_id:
            raise Exception(f"Failed to create session in database for '{identifier}': session_db_id is None")

        result = {
            "id": session_db_id,
            "username": display_name,
            "email": email,
            "sessionCode": session_code,
            "sessionDbId": session_db_id,
        }
        
        debug(f"Successfully created session for '{identifier}': {result}")
        return result
        
    except Exception as e:
        error(f"Error in _process_single_user_session for '{identifier}': {str(e)}")
        raise  # Re-raise the exception to be caught by the caller


async def _send_quiz_invitation_emails(created_sessions: list, assessment_id: int) -> dict:
    """
    Send quiz invitation emails to users who created sessions.

    Args:
        created_sessions: List of session data dictionaries
        assessment_id: ID of the assessment

    Returns:
        Dictionary with email sending results
    """
    if not EMAIL_SERVICE_AVAILABLE or not email_service:
        warning("Email service not available - skipping email sending")
        return {"emails_sent": 0, "emails_failed": 0, "errors": ["Email service not available"]}
    
    try:
        info(f"Starting email sending for assessment {assessment_id} with {len(created_sessions)} sessions")
        info(f"DEBUG: Email service SMTP username: {email_service.smtp_username}")
        info(f"DEBUG: Email service configured: {bool(email_service.smtp_username)}")
        
        # Get assessment details
        assessment_details = get_assessment_by_id(assessment_id, include_questions=False, include_answers=False)
        if not assessment_details:
            error(f"Assessment {assessment_id} not found for email sending")
            return {"emails_sent": 0, "emails_failed": 0, "errors": ["Assessment not found"]}

        assessment_name = assessment_details.get("name", "Quiz Assessment")
        if not assessment_name:
            warning(f"Assessment {assessment_id} has no name, using default")
            assessment_name = "Quiz Assessment"
            
        frontend_url = os.getenv("FRONTEND_URL", "http://localhost:5173")
        info(f"DEBUG: Assessment name: {assessment_name}, Frontend URL: {frontend_url}")

        # Prepare email invitations
        invitations = []
        for i, session in enumerate(created_sessions):
            info(f"DEBUG: Processing session {i}: {session}")
            
            # Ensure session is a valid dict
            if not session or not isinstance(session, dict):
                warning(f"Session {i} is not a valid dict: {session}")
                continue
                
            email = session.get("email")
            username = session.get("username")
            session_code = session.get("sessionCode")

            info(f"DEBUG: Session {i} - email: {email}, username: {username}, session_code: {session_code}")

            if email and username and session_code:
                # Check if we should send email to this address (domain filtering)
                should_send = email_service.should_send_email(email)
                info(f"DEBUG: Should send email to {email}: {should_send}")
                
                if not should_send:
                    info(f"Skipping email for {email} - excluded domain")
                    continue
                    
                # Generate quiz link using the session code
                from ...utils.hashid_utils import encode_session_code

                hashed_session_code = encode_session_code(session_code)
                quiz_link = f"{frontend_url}/quiz/{hashed_session_code}"

                invitation = {
                    "to_email": email,
                    "username": username,
                    "assessment_name": assessment_name,
                    "session_code": session_code,
                    "quiz_link": quiz_link,
                }
                invitations.append(invitation)
                info(f"DEBUG: Added invitation for {email}: {invitation}")
            else:
                info(f"DEBUG: Session {i} missing required data - email: {email}, username: {username}, session_code: {session_code}")

        info(f"DEBUG: Total invitations prepared: {len(invitations)}")
        for i, inv in enumerate(invitations):
            info(f"DEBUG: Invitation {i}: {inv}")

        if not invitations:
            info("DEBUG: No invitations to send")
            return {"emails_sent": 0, "emails_failed": 0, "errors": ["No valid email addresses found"]}

        # Send emails
        info(f"DEBUG: Calling send_bulk_quiz_invitations with {len(invitations)} invitations")
        email_results = await email_service.send_bulk_quiz_invitations(invitations)
        info(f"DEBUG: Email results received: {email_results}")

        # Count results
        emails_sent = sum(1 for success in email_results.values() if success)
        emails_failed = len(email_results) - emails_sent

        info(f"Email sending completed: {emails_sent} sent, {emails_failed} failed")

        return {"emails_sent": emails_sent, "emails_failed": emails_failed, "email_results": email_results}

    except Exception as e:
        error(f"Error sending quiz invitation emails: {str(e)}")
        import traceback
        error(f"Traceback: {traceback.format_exc()}")
        return {"emails_sent": 0, "emails_failed": len(created_sessions), "errors": [str(e)]}

@sessions_router.post("/admin/sessions")
async def generate_sessions(request_data: dict, _: None = Depends(rate_limiter)):
    """
    Generates assessment session codes for a list of users.
    It intelligently handles both usernames and email addresses as input.
    """
    try:
        # Validate request
        assessment_id, user_identifiers = _validate_session_request(request_data)

        # Debug logging
        debug(f"Session creation - Assessment ID: {assessment_id} (type: {type(assessment_id)})")
        debug(f"Session creation - User identifiers: {user_identifiers}")

        # Validate assessment exists
        assessment_data = validate_assessment_exists(assessment_id)
        if not assessment_data:
            error(f"validate_assessment_exists returned None for assessment_id: {assessment_id}")
            raise_http_exception(status_code=404, detail=f"Assessment with ID {assessment_id} not found.")
        debug(f"Session creation - Assessment found: {assessment_data['name']} (ID: {assessment_data['id']})")

        created_sessions = []
        failed_sessions = []
        default_email_domain = os.getenv("DEFAULT_EMAIL_DOMAIN", "example.com")

        # Process each user identifier
        for identifier in user_identifiers:
            try:
                if not identifier or not identifier.strip():
                    warning(f"Skipping empty identifier: '{identifier}'")
                    continue
                    
                session_data = _process_single_user_session(identifier, assessment_id, default_email_domain)
                if session_data and isinstance(session_data, dict):
                    created_sessions.append(session_data)
                else:
                    error(f"_process_single_user_session returned invalid data for '{identifier}': {session_data}")
                    failed_sessions.append(f"{identifier} (error: invalid session data returned)")

            except Exception as user_error:
                error(f"Error creating session for identifier '{identifier}': {str(user_error)}")
                failed_sessions.append(f"{identifier} (error: {str(user_error)})")

        # Prepare and return the final response
        if not created_sessions and failed_sessions:
            raise_http_exception(
                status_code=500, detail=f"Failed to create any sessions. Errors: {', '.join(failed_sessions)}"
            )

        # Send quiz invitation emails
        info(f"Attempting to send emails for {len(created_sessions)} sessions")
        info(f"Created sessions data: {created_sessions}")
        
        # Debug: Print detailed session data and filter out any None values
        valid_sessions = []
        for i, session in enumerate(created_sessions):
            if session is None:
                error(f"Session {i} is None - this should not happen")
                continue
            if not isinstance(session, dict):
                error(f"Session {i} is not a dict: {type(session)} - {session}")
                continue
            info(f"Session {i}: keys={list(session.keys())}, email={session.get('email')}, username={session.get('username')}")
            valid_sessions.append(session)
        
        # Use valid_sessions for email sending
        created_sessions = valid_sessions
        
        # Check email service configuration before trying to send
        info(f"DEBUG: EMAIL_SERVICE_AVAILABLE = {EMAIL_SERVICE_AVAILABLE}")
        info(f"DEBUG: email_service = {email_service}")
        if email_service:
            info(f"DEBUG: email_service.smtp_username = {email_service.smtp_username}")
        
        if not EMAIL_SERVICE_AVAILABLE or not email_service:
            error("Email service not available - skipping email sending")
            email_results = {"emails_sent": 0, "emails_failed": 0, "errors": ["Email service not available"]}
        elif not hasattr(email_service, 'smtp_username') or not email_service.smtp_username:
            error("Email service not properly configured - SMTP_USERNAME missing")
            email_results = {"emails_sent": 0, "emails_failed": 0, "errors": ["SMTP not configured"]}
        else:
            info(f"Email service configured with username: {email_service.smtp_username}")
            
            # Reinitialize email service to ensure fresh configuration
            from dotenv import load_dotenv
            from pathlib import Path
            backend_dir = Path(__file__).parent.parent.parent.parent
            env_path = backend_dir / '.env'
            load_dotenv(dotenv_path=env_path, override=True)
            
            # Reload email service configuration
            email_service.smtp_host = os.getenv("SMTP_HOST", "localhost")
            email_service.smtp_port = int(os.getenv("SMTP_PORT", "587"))
            email_service.smtp_username = os.getenv("SMTP_USERNAME")
            email_service.smtp_password = os.getenv("SMTP_PASSWORD")
            email_service.smtp_use_tls = os.getenv("SMTP_USE_TLS", "true").lower() == "true"
            email_service.from_email = os.getenv("FROM_EMAIL")
            email_service.from_name = os.getenv("FROM_NAME", "HERBIT Quiz System")
            
            info(f"Email service reloaded - SMTP_USERNAME: {email_service.smtp_username}")
            
            try:
                email_results = await _send_quiz_invitation_emails(created_sessions, assessment_id)
                info(f"Email results: {email_results}")
                
                # Ensure email_results is a valid dict
                if not email_results or not isinstance(email_results, dict):
                    error(f"Invalid email_results returned: {email_results}")
                    email_results = {"emails_sent": 0, "emails_failed": 0, "errors": ["Invalid email results returned"]}
                    
            except Exception as e:
                error(f"Exception in email sending: {str(e)}")
                import traceback
                error(f"Traceback: {traceback.format_exc()}")
                email_results = {"emails_sent": 0, "emails_failed": 0, "errors": [str(e)]}

        # Ensure email_results is properly initialized
        if not email_results:
            email_results = {"emails_sent": 0, "emails_failed": 0, "errors": ["Email results not initialized"]}

        response_data = {"sessions": created_sessions}
        if failed_sessions:
            response_data["warnings"] = f"Failed to create sessions for: {', '.join(failed_sessions)}"

        # Add email sending results to response
        if email_results.get("emails_sent", 0) > 0:
            response_data["emails_sent"] = email_results["emails_sent"]
        if email_results.get("emails_failed", 0) > 0:
            response_data["emails_failed"] = email_results["emails_failed"]

        # Prepare success message
        success_message = f"Generated {len(created_sessions)} session codes successfully [DEBUG: Code reloaded v3]."
        if email_results.get("emails_sent", 0) > 0:
            success_message += f" Sent {email_results['emails_sent']} email invitations."
        if email_results.get("emails_failed", 0) > 0:
            success_message += f" {email_results['emails_failed']} emails failed to send."

        # Validate response_data before hashing
        if not response_data or not isinstance(response_data, dict):
            error(f"Invalid response_data before hashing: {response_data}")
            raise_http_exception(status_code=500, detail="Failed to prepare response data.")

        hashed_data = hash_ids_in_response(response_data)
        
        if not hashed_data:
            error(f"hash_ids_in_response returned None for: {response_data}")
            raise_http_exception(status_code=500, detail="Failed to process response data.")
            
        return success_response(
            data=hashed_data,
            message=success_message,
        )

    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error(f"Unexpected error in generate_sessions: {str(e)}", exc_info=True)
        raise_http_exception(status_code=500, detail="An unexpected error occurred while generating sessions.")


# =============================================================================
# SUBMIT SESSION API ENDPOINT AND HELPER FUNCTIONS
# =============================================================================


def _validate_submit_session_request(session_code_input: str, user_id: str) -> str:
    """Validate submit session request parameters."""
    if not session_code_input:
        raise_http_exception(status_code=400, detail="Session code is required.")
    if not user_id:
        raise_http_exception(status_code=400, detail="User ID is required.")

    session_code = validate_session_code_format(session_code_input)
    debug(f"Decoded session code: '{session_code}'")
    return session_code


def _get_session_details_for_submit(session_code: str) -> dict:
    """Get session details and validate they exist."""
    session_details = get_session_and_assessment_details_by_code(session_code)
    if not session_details:
        debug(f"Session not found for code: '{session_code}'")
        raise_http_exception(status_code=404, detail="Invalid or expired session code.")

    debug(
        f"Session details: status='{session_details['session_status']}', "
        f"session_id={session_details.get('session_id')}"
    )
    return session_details


def _validate_session_user(session_details: dict, user_id: str):
    """Validate that user matches the session."""
    internal_user_id = get_or_create_user(user_id)
    debug(f"User validation: internal_user_id={internal_user_id}, session_user_id={session_details.get('user_id')}")

    if session_details["user_id"] != internal_user_id:
        debug(
            f"User mismatch: session belongs to user_id={session_details['user_id']}, "
            f"but request from user_id={internal_user_id}"
        )
        raise_http_exception(status_code=403, detail="User does not match session.")


@sessions_router.post("/submit_session")
def submit_session(request: SessionSubmissionRequest, _: None = Depends(rate_limiter)):
    """
    Submit a quiz session, marking it as completed and calculating final score.
    """
    try:
        session_code_input = request.session_code
        user_id = request.user_id

        debug(f"Submit session request: session_code='{session_code_input}', user_id='{user_id}'")

        # Validate request parameters
        session_code = _validate_submit_session_request(session_code_input, user_id)

        # Get session details
        session_details = _get_session_details_for_submit(session_code)

        # Handle different session statuses
        current_status = session_details["session_status"]

        if current_status == "completed":
            return handle_completed_session(session_details)
        elif current_status == "expired":
            return handle_expired_session(session_details)
        elif current_status == "pending":
            # Auto-start the session if it's still pending
            debug(f"Session is pending, auto-starting session: {session_details.get('session_id')}")
            start_session_in_db(session_details["session_id"])
            session_details["session_status"] = "in_progress"
        elif current_status != "in_progress":
            debug(f"Session not in progress. Current status: {current_status}")
            raise_http_exception(
                status_code=400, detail=f"Session is not in progress. Current status: {current_status}"
            )

        # Validate user matches session
        _validate_session_user(session_details, user_id)

        # Calculate scores
        obtained_score, total_possible_score, performance_level = calculate_session_scores(session_details)

        # Complete session in database
        complete_session_in_db(session_details, obtained_score)

        # Calculate percentage and prepare response
        percentage = (obtained_score / total_possible_score * 100) if total_possible_score > 0 else 0

        data = {
            "session_id": session_details["session_id"],
            "obtained_score": obtained_score,
            "total_possible_score": total_possible_score,
            "percentage": round(percentage, 2),
            "performance_level": performance_level,
            "status": "completed",
        }
        hashed_data = hash_ids_in_response(data)
        return success_response(data=hashed_data, message="Session submitted successfully")

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error submitting session {session_code_input}: {str(e)}", exc_info=True)
        raise_http_exception(status_code=500, detail=f"Error submitting session: {str(e)}")


# =============================================================================
# VALIDATE SESSION API ENDPOINT (REFACTORED)
# =============================================================================
@sessions_router.post("/validate_session_code")
def validate_session_code(request: SessionCodeRequest):
    session_code_input = request.session_code
    if not session_code_input:
        raise_http_exception(status_code=400, detail="Session code is required.")

    try:
        session_code = validate_session_code_format(session_code_input)

        session_details = get_session_for_start_or_validation(session_code)

        if not session_details:
            raise_http_exception(status_code=404, detail="Invalid or expired session code.")

        # All details, including question_selection_mode, are now in one object
        response_data = {
            "session_id": session_details["id"],
            "session_code": request.session_code,
            "assessment_id": session_details["assessment_id"],
            "assessment_name": session_details["assessment_name"],
            "is_final": session_details["is_final"],
            "username": session_details.get("username", ""),
            "session_status": session_details["session_status"],
            # You may need to add remaining_time_seconds logic back here or in the data function
            "remaining_time_seconds": session_details.get("remaining_time_seconds", 0),
            "started_at": session_details["started_at"].isoformat() if session_details["started_at"] else None,
            "completed_at": session_details["completed_at"].isoformat() if session_details["completed_at"] else None,
            "question_selection_mode": session_details["question_selection_mode"],
        }
        hashed_data = hash_ids_in_response(response_data)
        return success_response(data=hashed_data, message="Session code validated successfully")

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error validating session code {session_code}: {str(e)}", exc_info=True)
        raise_http_exception(status_code=500, detail=f"Server error: {str(e)}")


# =============================================================================
# START SESSION API ENDPOINT
# =============================================================================
@sessions_router.post("/start_session")
def start_session(request: StartSessionRequest):
    session_code_input = request.session_code
    if not session_code_input:
        raise_http_exception(status_code=400, detail="Session code is required.")

    try:
        session_code = validate_session_code_format(session_code_input)
        # 1. Get session details to validate
        session_details = get_session_for_start_or_validation(session_code)

        if not session_details:
            raise_http_exception(status_code=404, detail="Invalid or expired session code.")

        if session_details["session_status"] == "completed":
            raise_http_exception(
                status_code=409,
                detail=f"Session cannot be started. Current status: {session_details['session_status']}",
            )
        elif session_details["session_status"] == "in_progress":
            # Session is already in progress, this is okay - just return success
            info(f"Session {session_code} is already in progress, returning current state")
            response_data = {
                "session_id": session_details["id"],
                "session_code": session_code,
                "assessment_id": session_details["assessment_id"],
                "assessment_name": session_details["assessment_name"],
                "is_final": session_details["is_final"],
                "username": session_details.get("username", ""),
                "session_status": session_details["session_status"],
                "started_at": (session_details["started_at"].isoformat() if session_details["started_at"] else None),
                "completed_at": (
                    session_details["completed_at"].isoformat() if session_details["completed_at"] else None
                ),
            }
            hashed_data = hash_ids_in_response(response_data)
            return success_response(data=hashed_data, message="Session already in progress")
        elif session_details["session_status"] != "pending":
            raise_http_exception(
                status_code=409,
                detail=f"Session cannot be started. Current status: {session_details['session_status']}",
            )

        # 2. Update the session in the database
        rows_updated = start_session_in_db(session_details["id"])
        if rows_updated == 0:
            raise_http_exception(status_code=500, detail="Failed to start session, status might have changed.")

        # 3. Refresh session details to get updated timestamps
        updated_session_details = get_session_for_start_or_validation(session_code)

        # Format and return the response
        response_data = {
            "session_id": updated_session_details["id"],
            "session_code": session_code,
            "assessment_id": updated_session_details["assessment_id"],
            "assessment_name": updated_session_details["assessment_name"],
            "is_final": updated_session_details["is_final"],
            "username": updated_session_details.get("username", ""),
            "session_status": updated_session_details["session_status"],
            "started_at": (
                updated_session_details["started_at"].isoformat() if updated_session_details["started_at"] else None
            ),
            "completed_at": (
                updated_session_details["completed_at"].isoformat() if updated_session_details["completed_at"] else None
            ),
        }
        hashed_data = hash_ids_in_response(response_data)
        return success_response(data=hashed_data, message="Session started successfully")

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error starting session {session_code_input}: {str(e)}", exc_info=True)
        raise_http_exception(status_code=500, detail=f"Server error: {str(e)}")


# =============================================================================
# RESULT ON SESSION API ENDPOINT
# =============================================================================
@sessions_router.get("/admin/sessions/{session_id}/results")
async def get_session_results(session_id: str):
    try:
        # 1. Get the completed session details
        # Note: Assumes session_id can be a code or numeric ID. Hashing would be handled here if needed.
        session_dict = get_completed_session_for_results(session_id)

        if not session_dict:
            return error_response(
                message="Completed session not found",
                code=status.HTTP_404_NOT_FOUND,
            )

        # 2. Get the detailed answers for that session
        answered_questions_raw = get_session_answers_for_results(session_dict["id"])

        # The rest of the logic is for processing and formatting, which is fine to keep here.
        answered_questions, correct_answers, total_score = [], 0, 0
        for answer in answered_questions_raw:
            answer_dict = dict(answer)

            # Parse options safely
            options = safe_json_loads(answer_dict["options"], {})

            question_data = {
                "question": answer_dict["question"],
                "options": options,
                "userAnswer": answer_dict["user_answer"],
                "correctAnswerKey": answer_dict["correct_answer_key"],
                "isCorrect": answer_dict["is_correct"],
                "score": float(answer_dict["score"]) if answer_dict["score"] else 0,
                "level": answer_dict["level"],
            }

            answered_questions.append(question_data)

            if answer_dict["is_correct"]:
                correct_answers += 1

            total_score += question_data["score"]

        # Prepare response data (simplified for brevity)
        result_data = {
            "session_info": {
                "session_code": session_dict["session_code"],
                "username": session_dict["username"],
                "assessment_name": session_dict["assessment_name"],
                "completed_at": (session_dict["completed_at"].isoformat() if session_dict["completed_at"] else None),
                "question_selection_mode": session_dict["question_selection_mode"],
            },
            "score_summary": {
                "correct_answers": correct_answers,
                "total_questions": session_dict["total_questions"],
                "questions_attempted": len(answered_questions),
                "calculated_score": round(total_score, 2),
                "final_score": (
                    float(session_dict["score"]) if session_dict["score"] is not None else round(total_score, 2)
                ),
            },
            "answered_questions": answered_questions,
        }
        return success_response(data=result_data, message="Session results retrieved successfully")

    except Exception as e:
        error(f"Error getting session results for ID {session_id}: {str(e)}")
        return error_response(message=f"Error getting session results: {str(e)}", code=500)


# =============================================================================
# USER ON SESSION API ENDPOINT
# =============================================================================
@sessions_router.get("/admin/sessions/{session_code}/user")
async def get_session_user(session_code: str):
    try:
        if not session_code or len(session_code) != 6 or not session_code.isdigit():
            raise_http_exception(status_code=400, detail="Session code must be a 6-digit number")

        details = get_session_user_details(session_code)

        if not details:
            raise_http_exception(status_code=404, detail="Session code not found")

        username = details["display_name"] or details["external_id"]

        response_data = {
            "username": username,
            "assessment_id": details["assessment_id"],
            "assessment_name": details["assessment_name"],
            "is_final": details["is_final"],
            "session_status": details.get("session_status", "pending"),  # Fixed: use correct key and provide default
        }
        return hash_ids_in_response(response_data)

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error fetching session user for code {session_code}: {str(e)}")
        raise_http_exception(status_code=500, detail=f"Error fetching session user: {str(e)}")


# =============================================================================
# SESSION ON USER EMAIL API ENDPOINT
# =============================================================================


@sessions_router.get("/user/{email}/sessions")
async def get_user_sessions_by_email(email: str, current_user: dict = Depends(get_current_user)):
    """Get all sessions for a specific user identified by email."""
    try:
        # Verify that the requesting user can only access their own sessions
        user_email = current_user.get("email")
        if not user_email:
            raise_http_exception(status_code=401, detail="User email not found in authentication data")

        # Normalize emails for comparison (case-insensitive)
        if user_email.lower() != email.lower():
            warning(f"User {user_email} attempted to access sessions for {email}")
            raise_http_exception(status_code=403, detail="Access denied. You can only view your own sessions.")

        # 1. Get the user's internal ID using the new data access function
        user_id = get_user_id_by_email(email)

        if not user_id:
            warning(f"User with email {email} not found in database")
            # Return successfully with an empty list, matching original behavior
            return success_response(
                data={"sessions": [], "total": 0},
                message=f"No user found with email {email}",
            )

        # 2. Get all sessions for this user using the other new function
        sessions = get_sessions_by_user_id(user_id)

        # 3. Post-processing and response formatting (outside the DB connection block)
        hashed_sessions = hash_ids_in_response(sessions)

        return success_response(
            data={
                "sessions": hashed_sessions,
                "total": len(sessions),
            },
            message="User sessions retrieved successfully",
        )

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error getting sessions for user {email}: {str(e)}", exc_info=True)
        # Use a more generic error message for the user
        raise_http_exception(status_code=500, detail="An unexpected error occurred while retrieving user sessions.")


# =============================================================================
# SESSION ON USER EMAIL API ENDPOINT
# =============================================================================


@sessions_router.post("/admin/generate-link")
async def generate_quiz_link(request_data: dict, _: None = Depends(rate_limiter)):
    """Generate shareable quiz links using session codes"""
    try:
        session_codes = request_data.get("session_codes", [])

        info(f"🔍 GENERATE-LINK DEBUG - Session codes received: {session_codes}")
        info(f"🔍 GENERATE-LINK DEBUG - Full request: {request_data}")

        if not session_codes:
            raise_http_exception(status_code=400, detail="Session codes are required to generate quiz links.")

        frontend_url = os.getenv("FRONTEND_URL", "http://localhost:5173")

        # Generate links for each session code
        quiz_links = []
        for session_code in session_codes:
            try:
                # Validate session code format (should be 6 digits)
                if not session_code or len(str(session_code)) != 6 or not str(session_code).isdigit():
                    warning(f"Invalid session code format: {session_code}")
                    continue

                # Encode the session code for the URL
                hashed_session_code = encode_session_code(session_code)
                quiz_link = f"{frontend_url}/quiz/{hashed_session_code}"

                quiz_links.append(
                    {"session_code": session_code, "quiz_link": quiz_link, "hashed_session_code": hashed_session_code}
                )

                info(f"Generated quiz link for session {session_code}: {quiz_link}")

            except Exception as e:
                error(f"Failed to generate link for session code {session_code}: {str(e)}")
                continue

        if not quiz_links:
            raise_http_exception(status_code=500, detail="Failed to generate any quiz links.")

        # Return the first link as the main link (for backward compatibility)
        # and all links in the data
        main_link = quiz_links[0]["quiz_link"] if quiz_links else ""

        response_data = {"link": main_link, "quiz_links": quiz_links, "total_links": len(quiz_links)}

        return success_response(data=response_data, message=f"Generated {len(quiz_links)} quiz link(s) successfully")

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error generating quiz link: {str(e)}")
        raise_http_exception(status_code=500, detail=f"Error generating quiz link: {str(e)}")


@sessions_router.get("/admin/debug/assessments")
async def debug_assessments():
    """Debug endpoint to check what assessments exist in the database"""
    try:
        from ...models.assessment_manager import get_assessments_paginated

        # Get all assessments
        assessments, total = get_assessments_paginated(limit=100, offset=0)

        debug_info = {
            "total_assessments": total,
            "assessments": [
                {
                    "id": a["id"],
                    "name": a["name"],
                    "description": a.get("description", "")[:100] + "..." if a.get("description", "") else "",
                    "is_final": a.get("is_final", False),
                    "question_selection_mode": a.get("question_selection_mode", ""),
                }
                for a in assessments
            ],
        }

        return success_response(data=debug_info, message=f"Found {total} assessments in database")

    except Exception as e:
        error(f"Error in debug assessments endpoint: {str(e)}")
        return error_response(message=f"Error fetching assessments: {str(e)}", code=500)
