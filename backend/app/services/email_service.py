"""
Email service for sending quiz session invitations.
Handles SMTP configuration, email templates, and email delivery.
"""

import os
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from typing import Dict, List
from pathlib import Path

import aiosmtplib
from jinja2 import Template
from dotenv import load_dotenv

from ..utils.logger import error, info, warning
from ..config.config import EXCLUDED_EMAIL_DOMAINS

# Load environment variables from the correct location
backend_dir = Path(__file__).parent.parent.parent
env_path = backend_dir / '.env'
info(f"Loading .env from: {env_path}")
load_dotenv(dotenv_path=env_path)

# If that doesn't work, try from current working directory
if not os.getenv("SMTP_USERNAME"):
    info("SMTP_USERNAME not found, trying to load from current directory")
    load_dotenv()

# Debug what we actually loaded
info(f"Environment variables loaded:")
info(f"  SMTP_HOST: {os.getenv('SMTP_HOST')}")
info(f"  SMTP_USERNAME: {os.getenv('SMTP_USERNAME')}")
info(f"  FROM_EMAIL: {os.getenv('FROM_EMAIL')}")


class EmailService:
    """Service for sending quiz invitation emails."""

    def __init__(self):
        """Initialize email service with SMTP configuration."""
        self.smtp_host = os.getenv("SMTP_HOST", "localhost")
        self.smtp_port = int(os.getenv("SMTP_PORT", "587"))
        self.smtp_username = os.getenv("SMTP_USERNAME")
        self.smtp_password = os.getenv("SMTP_PASSWORD")
        self.smtp_use_tls = os.getenv("SMTP_USE_TLS", "true").lower() == "true"
        self.from_email = os.getenv("FROM_EMAIL")
        self.from_name = os.getenv("FROM_NAME", "HERBIT Quiz System")

        # Debug logging for SMTP configuration
        info(f"EmailService initialized with:")
        info(f"  SMTP_HOST: {self.smtp_host}")
        info(f"  SMTP_PORT: {self.smtp_port}")
        info(f"  SMTP_USERNAME: {'configured' if self.smtp_username else 'NOT SET'}")
        info(f"  SMTP_PASSWORD: {'configured' if self.smtp_password else 'NOT SET'}")
        info(f"  FROM_EMAIL: {'configured' if self.from_email else 'NOT SET'}")

        # Email template
        self.email_template = self._get_email_template()

    def _get_email_template(self) -> str:
        """Get the HTML email template for quiz invitations."""
        return """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quiz Invitation - HERBIT</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #7f8c8d;
            font-size: 16px;
        }
        .content {
            margin-bottom: 30px;
        }
        .quiz-info {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #3498db;
        }
        .quiz-link {
            display: inline-block;
            background-color: #3498db;
            color: white;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin: 20px 0;
            transition: background-color 0.3s;
        }
        .quiz-link:hover {
            background-color: #2980b9;
        }
        .session-code {
            font-family: 'Courier New', monospace;
            font-size: 18px;
            font-weight: bold;
            color: #e74c3c;
            background-color: #f8f9fa;
            padding: 8px 12px;
            border-radius: 4px;
            display: inline-block;
            margin: 5px 0;
        }
        .instructions {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
            color: #7f8c8d;
            font-size: 14px;
        }
        .warning {
            color: #e74c3c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🌿 HERBIT</div>
            <div class="subtitle">Quiz Assessment System</div>
        </div>

        <div class="content">
            <h2>You're Invited to Take a Quiz!</h2>

            <p>Hello <strong>{{ username }}</strong>,</p>

            <p>You have been invited to participate in a quiz assessment. Please find your quiz details below:</p>

            <div class="quiz-info">
                <h3>📋 Quiz Details</h3>
                <p><strong>Assessment:</strong> {{ assessment_name }}</p>
                <p><strong>Your Session Code:</strong> <span class="session-code">{{ session_code }}</span></p>
            </div>

            <div class="instructions">
                <h4>📝 Instructions:</h4>
                <ol>
                    <li>Click the button below to access your quiz</li>
                    <li>Enter your session code when prompted</li>
                    <li>Complete all questions within the time limit</li>
                    <li><span class="warning">Do not refresh or close the browser during the quiz</span></li>
                </ol>
            </div>

            <div style="text-align: center;">
                <a href="{{ quiz_link }}" class="quiz-link">🚀 Start Quiz</a>
            </div>

            <p><strong>Alternative Access:</strong> If the button doesn't work, copy and paste this link into your browser:</p>
            <p style="word-break: break-all; background-color: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace;">{{ quiz_link }}</p>
        </div>

        <div class="footer">
            <p>This is an automated message from the HERBIT Quiz System.</p>
            <p>If you have any questions, please contact your administrator.</p>
        </div>
    </div>
</body>
</html>
        """

    def should_send_email(self, email: str) -> bool:
        """
        Check if email should be sent.
        Returns False for emails from excluded domains (like improwised.com).
        """
        if not email or "@" not in email:
            return False
        
        # Extract domain from email
        domain = email.split("@")[1].lower()
        
        # Check if domain is in excluded list
        if domain in EXCLUDED_EMAIL_DOMAINS:
            info(f"Email {email} from domain {domain} is excluded from receiving invitations")
            return False
            
        return True

    async def send_quiz_invitation(
        self, to_email: str, username: str, assessment_name: str, session_code: str, quiz_link: str
    ) -> bool:
        """
        Send quiz invitation email to a user.

        Args:
            to_email: Recipient email address
            username: User's display name
            assessment_name: Name of the assessment
            session_code: Session code for the quiz
            quiz_link: Direct link to access the quiz

        Returns:
            bool: True if email was sent successfully, False otherwise
        """
        try:
            # Check if we should send email to this address
            if not self.should_send_email(to_email):
                info(f"Email sending skipped for {to_email} due to domain filtering")
                return False
                
            # Check SMTP configuration
            if not self.smtp_username or not self.smtp_password or not self.from_email:
                error(f"SMTP configuration incomplete - cannot send email to {to_email}")
                error(f"SMTP_USERNAME: {'configured' if self.smtp_username else 'missing'}")
                error(f"SMTP_PASSWORD: {'configured' if self.smtp_password else 'missing'}")
                error(f"FROM_EMAIL: {'configured' if self.from_email else 'missing'}")
                return False

            info(f"Attempting to send email to: {to_email}")
            info(f"SMTP Config: host={self.smtp_host}, port={self.smtp_port}, username={self.smtp_username}")

            info(f"Preparing to send email to {to_email}")

            # Render email template
            template = Template(self.email_template)
            html_content = template.render(
                username=username, assessment_name=assessment_name, session_code=session_code, quiz_link=quiz_link
            )

            # Create email message
            message = MIMEMultipart("alternative")
            message["Subject"] = f"Quiz Invitation: {assessment_name}"
            message["From"] = f"{self.from_name} <{self.from_email}>"
            message["To"] = to_email

            # Add HTML content
            html_part = MIMEText(html_content, "html")
            message.attach(html_part)

            info(f"Sending email to {to_email} with subject: Quiz Invitation: {assessment_name}")

            # Send email using aiosmtplib
            await aiosmtplib.send(
                message,
                hostname=self.smtp_host,
                port=self.smtp_port,
                start_tls=self.smtp_use_tls,
                username=self.smtp_username if self.smtp_username else None,
                password=self.smtp_password if self.smtp_password else None,
            )

            info(f"Quiz invitation email sent successfully to {to_email}")
            return True

        except Exception as e:
            error(f"SMTP ERROR - Failed to send email to {to_email}: {str(e)}")
            error(f"SMTP Config - Host: {self.smtp_host}, Port: {self.smtp_port}, Username: {self.smtp_username}")
            return False

    async def send_bulk_quiz_invitations(self, invitations: List[Dict[str, str]]) -> Dict[str, bool]:
        """
        Send quiz invitations to multiple users.

        Args:
            invitations: List of dictionaries containing email details
                Each dict should have: to_email, username, assessment_name, session_code, quiz_link

        Returns:
            Dict mapping email addresses to success status
        """
        info(f"send_bulk_quiz_invitations called with {len(invitations)} invitations")
        for i, invitation in enumerate(invitations):
            info(f"Invitation {i}: {invitation}")
            
        results = {}

        # Validate invitations list
        if not invitations:
            warning("No invitations provided to send_bulk_quiz_invitations")
            return results

        # Send emails concurrently
        tasks = []
        for invitation in invitations:
            try:
                # Validate invitation data
                if not invitation or not isinstance(invitation, dict):
                    error(f"Invalid invitation data: {invitation}")
                    continue
                    
                required_fields = ["to_email", "username", "assessment_name", "session_code", "quiz_link"]
                missing_fields = [field for field in required_fields if not invitation.get(field)]
                
                if missing_fields:
                    error(f"Invitation missing required fields: {missing_fields}. Invitation: {invitation}")
                    results[invitation.get("to_email", "unknown")] = False
                    continue
                
                task = self.send_quiz_invitation(
                    to_email=invitation["to_email"],
                    username=invitation["username"],
                    assessment_name=invitation["assessment_name"],
                    session_code=invitation["session_code"],
                    quiz_link=invitation["quiz_link"],
                )
                tasks.append((invitation["to_email"], task))
            except Exception as e:
                error(f"Error preparing email task for invitation {invitation}: {str(e)}")
                email_address = invitation.get("to_email", "unknown") if isinstance(invitation, dict) else "unknown"
                results[email_address] = False

        # Wait for all emails to be sent
        for email, task in tasks:
            try:
                success = await task
                results[email] = success
                info(f"Email task result for {email}: {success}")
            except Exception as e:
                error(f"Error sending email to {email}: {str(e)}")
                results[email] = False

        info(f"send_bulk_quiz_invitations returning: {results}")
        return results


# Global email service instance
email_service = EmailService()
