#!/usr/bin/env python3
"""
Test script to create a session and check email sending
"""

import requests
import json

# First, let's check what assessments exist
print("=== Checking Available Assessments ===")
try:
    assessments_response = requests.get("http://localhost:8000/api/admin/debug/assessments")
    if assessments_response.status_code == 200:
        assessments_data = assessments_response.json()
        print(f"Available assessments: {assessments_data}")
        if assessments_data.get('data', {}).get('assessments'):
            first_assessment = assessments_data['data']['assessments'][0]
            assessment_id = first_assessment['id']
            print(f"Using assessment ID: {assessment_id}")
        else:
            assessment_id = 1
            print("No assessments found, using ID 1")
    else:
        assessment_id = 1
        print("Could not fetch assessments, using ID 1")
except:
    assessment_id = 1
    print("Error fetching assessments, using ID 1")

# Test data
test_data = {
    "assessment_id": assessment_id,
    "usernames": "<EMAIL>"  # Use a Gmail address that should receive emails
}

print("=== Testing Session Creation with Email Sending ===")
print(f"Test data: {test_data}")

try:
    # Make API call to create session
    response = requests.post(
        "http://localhost:8000/api/admin/sessions",
        json=test_data,
        headers={"Content-Type": "application/json"}
    )
    
    print(f"\nResponse Status: {response.status_code}")
    print(f"Response Headers: {dict(response.headers)}")
    
    if response.status_code == 200:
        response_data = response.json()
        print(f"\nResponse Data: {json.dumps(response_data, indent=2)}")
        
        # Check if email sending information is in the response
        if 'data' in response_data:
            data = response_data['data']
            if 'emails_sent' in data:
                print(f"\n✅ Emails sent: {data['emails_sent']}")
            if 'emails_failed' in data:
                print(f"❌ Emails failed: {data['emails_failed']}")
            if 'sessions' in data:
                print(f"📝 Sessions created: {len(data['sessions'])}")
        
        # Check message for email info
        message = response_data.get('message', '')
        if 'email' in message.lower():
            print(f"\n📧 Message contains email info: {message}")
            
    else:
        print(f"\n❌ Error Response: {response.text}")
        
except requests.exceptions.ConnectionError:
    print("❌ Could not connect to backend. Is it running on port 8000?")
except Exception as e:
    print(f"❌ Error: {e}")
